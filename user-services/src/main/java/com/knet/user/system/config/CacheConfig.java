package com.knet.user.system.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static com.knet.common.constants.UserServicesConstants.USER_LIST_CACHE_NAME;

/**
 * <AUTHOR>
 * @date 2025/12/19 15:25
 * @description: 缓存配置类，专门配置不同缓存的过期时间和序列化方式
 */
@Configuration
public class CacheConfig {

    /**
     * 配置CacheManager，用于@Cacheable等注解
     * 支持不同缓存名称的不同配置
     */
    @Bean
    public CacheManager cacheManager(LettuceConnectionFactory lettuceConnectionFactory) {
        // 创建自定义的ObjectMapper，解决序列化问题
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        objectMapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        // 使用自定义ObjectMapper的序列化器
        GenericJackson2JsonRedisSerializer jsonRedisSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);
        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                // 设置缓存的默认过期时间为30分钟
                .entryTtl(Duration.ofMinutes(30))
                // 设置 key为string序列化
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                // 设置value为json序列化
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jsonRedisSerializer))
                // 不缓存空值
                .disableCachingNullValues()
                // 设置缓存key前缀
                .computePrefixWith(name -> "user-service:" + name + ":");
        // 针对不同缓存名称的特定配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 用户列表缓存配置 - 30分钟过期
        cacheConfigurations.put(USER_LIST_CACHE_NAME, defaultConfig
                .entryTtl(Duration.ofMinutes(30))
                .computePrefixWith(name -> "user-service:userList:"));

        return RedisCacheManager.builder(lettuceConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
