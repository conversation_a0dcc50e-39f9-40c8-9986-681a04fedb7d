package com.knet.user.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.user.model.dto.req.UserEditRequest;
import com.knet.user.model.dto.req.UserQueryRequest;
import com.knet.user.model.dto.req.UserRechargeQueryRequest;
import com.knet.user.model.dto.req.UserSaveRequest;
import com.knet.user.model.dto.rsp.UserInfoDtoResp;
import com.knet.user.model.dto.rsp.UserRechargeRecordResp;
import com.knet.user.model.entity.SysUser;

/**
 * <AUTHOR>
 * @date 2025/2/12 15:43
 * @description: 用户接口
 */
public interface ISysUserService extends IService<SysUser> {

    /**
     * 创建用户
     *
     * @param userSaveRequest 用户保存请求体
     * @return User
     */
    SysUser createUser(UserSaveRequest userSaveRequest);

    /**
     * 查询用户列表
     *
     * @param request 查询用户请求体
     * @return 返回用户列表
     */
    IPage<UserInfoDtoResp> listUser(UserQueryRequest request);

    /**
     * 根据用户ID查询用户
     *
     * @param id id
     * @return dto
     */
    UserInfoDtoResp getUserById(Long id);

    /**
     * 更新用户
     *
     * @param request 更新用户请求体
     */
    void updateUser(UserEditRequest request);

    /**
     * 删除用户(逻辑删除)
     *
     * @param id 用户ID
     * @return 返回删除结果
     */
    Boolean deleteUser(Long id);


    /**
     * 查询用户充值/扣款 记录
     *
     * @param request 请求
     * @return 响应
     */
    IPage<UserRechargeRecordResp> operationRecord(UserRechargeQueryRequest request);
}
