# B2B 电商父子订单状态流转图

该流程图完整展示了父订单和子订单在 B2B 电商系统中的状态流转关系，包含支付、发货、售后等核心业务流程：

```mermaid
graph TB
    %% 父订单状态流转
    subgraph 父订单状态
        PG_PENDING_PAYMENT(待支付) --> |支付发起| PG_IN_PAYMENT(支付中)
        PG_IN_PAYMENT --> |支付成功| PG_PAID(已支付)
        PG_IN_PAYMENT --> |支付失败| PG_PENDING_PAYMENT
        
        PG_PAID --> |准备发货| PG_PENDING_SHIPMENT(待发货)
        PG_PENDING_SHIPMENT --> |全部发货| PG_SHIPPED(已发货)
        PG_PENDING_SHIPMENT --> |部分发货| PG_PARTIALLY_SHIPPED(部分发货)
        PG_PARTIALLY_SHIPPED --> |剩余发货| PG_SHIPPED
        
        PG_SHIPPED --> |物流签收| PG_DELIVERED(已送达)
        PG_DELIVERED --> |确认完成| PG_COMPLETED(已完成)
        
        %% 售后路径
        PG_PAID --> |申请退款| PG_REFUNDING(退款中)
        PG_PENDING_SHIPMENT --> |申请退款| PG_REFUNDING
        PG_REFUNDING --> |退款完成| PG_PARTIALLY_CANCELLED(部分取消)
        PG_REFUNDING --> |全额退款| PG_CANCELLED(已取消)
        
        PG_SHIPPED --> |申请退货| PG_RETURNING(退货中)
        PG_DELIVERED --> |申请退货| PG_RETURNING
        PG_RETURNING --> |退货完成| PG_PARTIALLY_CANCELLED
        PG_RETURNING --> |全部退货| PG_CANCELLED
        
        %% 取消路径
        PG_PENDING_PAYMENT --> |订单取消| PG_CANCELLED
        PG_IN_PAYMENT --> |订单取消| PG_CANCELLED
        PG_PARTIALLY_SHIPPED --> |剩余取消| PG_PARTIALLY_CANCELLED
    end
    
    %% 子订单状态流转
    subgraph 子订单状态
        PI_PENDING_PAYMENT(待支付) --> |支付成功| PI_PAID(已支付)
        PI_PAID --> |发货准备| PI_PENDING_SHIPMENT(待发货)
        PI_PENDING_SHIPMENT --> |发货完成| PI_SHIPPED(已发货)
        PI_SHIPPED --> |物流签收| PI_DELIVERED(已送达)
        PI_DELIVERED --> |确认完成| PI_COMPLETED(已完成)
        
        %% 取消路径
        PI_PENDING_PAYMENT --> |订单取消| PI_CANCELLED(已取消)
        PI_PAID --> |订单取消| PI_CANCELLED
        PI_PENDING_SHIPMENT --> |订单取消| PI_CANCELLED
    end
    
    %% 父子订单状态关联
    PI_PAID --> |所有子订单支付成功| PG_PAID
    PI_SHIPPED --> |子订单发货触发| PG_PARTIALLY_SHIPPED
    PI_CANCELLED --> |子订单取消触发| PG_PARTIALLY_CANCELLED
    PI_CANCELLED --> |所有子订单取消| PG_CANCELLED 
    
mermaid 
```