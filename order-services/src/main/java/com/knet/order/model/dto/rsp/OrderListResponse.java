package com.knet.order.model.dto.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.knet.common.base.BaseResponse;
import com.knet.common.enums.KnetOrderGroupStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * <AUTHOR>
 * @date 2025/6/6 10:00
 * @description: 订单列表响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "订单列表响应")
public class OrderListResponse extends BaseResponse {

    @Schema(description = "父订单列表")
    private List<ParentOrderResponse> orders;

    /**
     * 父订单响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "父订单响应")
    public static class ParentOrderResponse {

        @Schema(description = "父订单ID")
        private String parentOrderId;

        @Schema(description = "用户ID")
        private Long userId;

        @Schema(description = "订单状态")
        private KnetOrderGroupStatus status;

        @Schema(description = "总商品数量")
        private Integer totalQuantity;

        @Schema(description = "订单总金额（美元）")
        private String totalAmount;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = NORM_DATETIME_PATTERN)
        @Schema(description = "订单创建时间")
        private Date createTime;

        @Schema(description = "子订单汇总列表")
        private List<SubOrderSummaryResponse> subOrders;
    }

    /**
     * 子订单汇总响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "子订单汇总响应")
    public static class SubOrderSummaryResponse {

        @Schema(description = "父订单ID")
        private String parentOrderId;

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "商品名称")
        private String productName;

        @Schema(description = "商品图片URL")
        private String imageUrl;

        @Schema(description = "聚合后的商品总数量")
        private Integer totalQuantity;

        @Schema(description = "平均价格（美元）")
        private String avgPrice;

        @Schema(description = "总价格（美元）")
        private String totalPrice;

        @Schema(description = "子订单状态")
        private KnetOrderGroupStatus status;
    }
}
